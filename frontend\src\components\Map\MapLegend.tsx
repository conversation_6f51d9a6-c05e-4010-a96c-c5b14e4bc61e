import React, { useState, useEffect, useRef } from 'react';
import { API_CONFIG } from '../../config';
import { WMSLayer } from '../../services/geoserverService';
import './Styling/MapLegend.css';


interface MapLegendProps {
  visibleLayers: WMSLayer[];
  position?: 'bottomleft' | 'bottomright' | 'topleft' | 'topright';
  maxVisibleItems?: number;
}

const MapLegend: React.FC<MapLegendProps> = ({
  visibleLayers = [],
  position = 'bottomleft',
  maxVisibleItems = 4
}) => {
  debugger;
  const [isMinimized, setIsMinimized] = useState(false);
  const [legendImages, setLegendImages] = useState<{ [key: string]: string }>({});
  const legendPanelRef = useRef<HTMLDivElement>(null);

  // Disable Leaflet map interactions on legend area
  useEffect(() => {
    const legendElement = legendPanelRef.current;
    if (!legendElement) return;

    // Add Leaflet's control class to disable map interactions
    legendElement.classList.add('leaflet-control');

    // Create event handlers that completely stop propagation
    const stopAllEvents = (e: Event) => {
      e.stopPropagation();
      e.stopImmediatePropagation();
      e.preventDefault();
    };

    const handleWheel = (e: WheelEvent) => {
      e.stopPropagation();
      e.stopImmediatePropagation();
      e.preventDefault();

      // Find the scrollable content within the legend
      const legendContent = legendElement.querySelector('.legend-content') as HTMLElement;
      if (legendContent) {
        // Manually scroll the legend content
        legendContent.scrollTop += e.deltaY * 0.5; // Adjust scroll speed
      }
    };

    // Add all event listeners with capture: true to intercept before Leaflet
    const events = ['mousedown', 'mouseup', 'mousemove', 'click', 'dblclick', 'contextmenu'];
    const touchEvents = ['touchstart', 'touchmove', 'touchend', 'touchcancel'];

    events.forEach(eventType => {
      legendElement.addEventListener(eventType, stopAllEvents, { capture: true, passive: false });
    });

    touchEvents.forEach(eventType => {
      legendElement.addEventListener(eventType, stopAllEvents, { capture: true, passive: false });
    });

    // Special handling for wheel events
    legendElement.addEventListener('wheel', handleWheel, { capture: true, passive: false });

    // Cleanup function
    return () => {
      events.forEach(eventType => {
        legendElement.removeEventListener(eventType, stopAllEvents, { capture: true });
      });

      touchEvents.forEach(eventType => {
        legendElement.removeEventListener(eventType, stopAllEvents, { capture: true });
      });

      legendElement.removeEventListener('wheel', handleWheel, { capture: true });
    };
  }, []);

  // Fetch legend images for visible layers
  useEffect(() => {
    const fetchLegends = async () => {
      if (visibleLayers.length === 0) {
        setLegendImages({});
        return;
      }

      const legendPromises = visibleLayers.map(async (layer) => {
        try {
          const legendUrl = `${API_CONFIG.BASE_URL}/ows/legend?layer=${encodeURIComponent(layer.name)}&format=image/png`;
          const response = await fetch(legendUrl);

          if (response.ok) {
            const blob = await response.blob();
            const imageUrl = URL.createObjectURL(blob);
            return { [layer.name]: imageUrl };
          } else {
            return { [layer.name]: null };
          }
        } catch (error) {
          console.error(`Error fetching legend for ${layer.name}:`, error);
          return { [layer.name]: null };
        }
      });

      const results = await Promise.all(legendPromises);
      debugger;
      const newLegendImages = results.reduce((acc: { [key: string]: string }, curr) => {
        if ((curr as { [key: string]: string })[Object.keys(curr)[0]] !== null) {
          acc[Object.keys(curr)[0]] = (curr as { [key: string]: string })[Object.keys(curr)[0]];
        }
        return acc;
      }, {});
      setLegendImages(newLegendImages);
    };

    fetchLegends();

    // Cleanup blob URLs when component unmounts or layers change
    return () => {
      Object.values(legendImages).forEach(url => {
        if (url && typeof url === 'string') {
          URL.revokeObjectURL(url);
        }
      });
    };
  }, [visibleLayers]);

  // Don't render if no visible layers
  if (visibleLayers.length === 0) {
    return null;
  }

  const positionClass = `legend-${position}`;

  // Sort layers by render order for legend display (reverse order for legend - top priority shows first)
  const sortedLayers = [...visibleLayers].sort((a, b) => {
    const priorityA = (a as any).renderOrder?.priority || 0;
    const priorityB = (b as any).renderOrder?.priority || 0;
    return priorityB - priorityA; // Higher priority shows first in legend (reverse of map order)
  });

  const displayLayers = sortedLayers.slice(0, maxVisibleItems);

  // Prevent map events when interacting with legend - more aggressive approach
  const handleMouseEvents = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
  };

  const handleWheelEvents = (e: React.WheelEvent) => {
    e.stopPropagation();
    e.preventDefault();

    // Manually handle scrolling for the legend content
    const legendContent = e.currentTarget.querySelector('.legend-content');
    if (legendContent) {
      legendContent.scrollTop += e.deltaY;
    }
  };

  const handleTouchEvents = (e: React.TouchEvent) => {
    e.stopPropagation();
    e.preventDefault();
  };

  const handleKeyEvents = (e: React.KeyboardEvent) => {
    e.stopPropagation();
    e.preventDefault();
  };

  return (
    <div
      ref={legendPanelRef}
      className={`map-legend-panel ${positionClass} ${isMinimized ? 'minimized' : 'expanded'}`}
      onMouseDown={handleMouseEvents}
      onMouseUp={handleMouseEvents}
      onMouseMove={handleMouseEvents}
      onClick={handleMouseEvents}
      onWheel={handleWheelEvents}
      onTouchStart={handleTouchEvents}
      onTouchMove={handleTouchEvents}
      onTouchEnd={handleTouchEvents}
      onKeyDown={handleKeyEvents}
      onKeyUp={handleKeyEvents}
    >
      {isMinimized ? (
        <div
          className="legend-tab"
          onClick={() => setIsMinimized(false)}
          title="Click to expand legend"
        >
          <span>Legend ({visibleLayers.length})</span>
        </div>
      ) : (
        <>
          <div className="legend-header">
            <h6 className="legend-title">Legend</h6>
            <button
              className="legend-minimize-btn"
              onClick={() => setIsMinimized(true)}
              title="Minimize legend"
            >
              −
            </button>
          </div>

          <div className="legend-content">
            {displayLayers.map((layer) => (
              <div key={layer.name} className="legend-item-card">
                {legendImages[layer.name] ? (
                  <img
                    src={legendImages[layer.name]}
                    alt={`Legend for ${layer.title || layer.name}`}
                    className="legend-image"
                  />
                ) : (
                  <div className="legend-placeholder"></div>
                )}

                <span className="legend-layer-name">
                  {layer.title || layer.name}
                </span>
              </div>
            ))}

            {visibleLayers.length > maxVisibleItems && (
              <div className="legend-more-info">
                +{visibleLayers.length - maxVisibleItems} more layers
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
};

export default MapLegend;
